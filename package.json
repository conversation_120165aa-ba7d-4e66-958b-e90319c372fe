{"private": true, "type": "module", "scripts": {"dev": "plugma dev", "build": "plugma build", "preview": "plugma preview", "release": "plugma release"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.28.0", "@figma/plugin-typings": "^1.113.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.1", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "plugma": "^1.2.9", "typescript": "~5.6.3", "typescript-eslint": "^8.33.1", "vite": "^5.4.19"}, "plugma": {"manifest": {"id": "plugmate-replace", "name": "plugmate", "main": "src/main.ts", "ui": "src/ui.tsx", "editorType": ["figma", "figjam"], "networkAccess": {"allowedDomains": ["none"], "devAllowedDomains": ["http://localhost:*", "ws://localhost:9001"]}}}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}